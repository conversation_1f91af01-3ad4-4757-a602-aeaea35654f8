---
import Layout from "@/layouts/Layout.astro";

export function getStaticPaths() {
  return [{ params: { lang: "fr" } }, { params: { lang: "en" } }];
}

const { lang } = Astro.params;
const supportedLangs = ["fr", "en"] as const;
function isSupportedLang(str: string | undefined): str is "fr" | "en" {
  return supportedLangs.includes(str as any);
}
const safeLang = isSupportedLang(lang) ? lang : "fr";

const i18n = (await import(`../../i18n/${safeLang}.json`)).default;
---

<Layout i18n={i18n} lang={safeLang} pageKey="home" />
