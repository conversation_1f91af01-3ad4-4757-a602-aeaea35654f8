---
import ThemeToggle from "./ThemeToggle.astro";
import LanguageSwitcher from "./LanguageSwitcher.astro";

interface Lang {
  code: string;
  label: string;
}

interface Props {
  i18n?: any;
  languages: Lang[];
  currentLang?: string;
  title?: string;
  ariaLabel?: string;
  direction?: "column" | "row";
  dense?: boolean;
  class?: string;
}

const {
  i18n,
  languages = [
    { code: "fr", label: "Français" },
    { code: "en", label: "English" },
  ],
  currentLang = "fr",
  title = i18n?.quickAccess?.actions ?? "Actions",
  ariaLabel = i18n?.quickAccess?.actions ?? "Actions",
  direction = "column",
  dense = false,
  class: extra = "",
} = Astro.props as Props;
---

<section
  class={`qa-actions-container ${direction} ${dense ? "dense" : ""} ${extra}`}
  role="group"
  aria-label={ariaLabel}
>
  {title && <h4 class="qa-actions-title no-select">{title}</h4>}
  <div class="qa-actions-row">
    <ThemeToggle />
  </div>
  <div class="qa-actions-row">
    <LanguageSwitcher languages={languages} currentLang={currentLang} />
  </div>

  <!-- Slot si un jour tu veux ajouter d’autres actions -->
  <slot />
</section>

<style>
  .qa-actions-container {
    display: flex;
    gap: var(--spacing-8);
  }
  .qa-actions-container.row {
    flex-direction: row;
    align-items: center;
  }
  .qa-actions-container.column {
    flex-direction: column;
  }

  .qa-actions-container.dense {
    gap: var(--spacing-4);
  }
  .qa-actions-title {
    margin: 0;
    font-size: 0.95rem;
    color: var(--color-text-secondary);
  }

  .qa-actions-row {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
  }

  @media (max-width: 720px) {
    .qa-actions-container.row {
      flex-direction: column;
      align-items: flex-start;
    }
  }
</style>
