# 🚀 Roadmap - Portfolio <PERSON>cher

Ce document liste les étapes de développement passées, en cours et futures pour le portfolio.

---

## ✅ Déjà réalisé

- [x] Mise en place du projet avec **Astro**
- [x] Configuration TypeScript & ESLint
- [x] Système de **design global** (variables CSS, thèmes clair/sombre)
- [x] Création des sections principales : Hero, About, Projects, Contact
- [x] Composants réutilisables : <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, QuickAccess, ThemeToggle
- [x] Gestion du multilingue (**fr/en**) via JSON i18n
- [x] Données dynamiques pour les projets (`src/data/projects.json`)
- [x] Optimisation des images (WebP, SVG)
- [x] Ajout du SEO (`SEO.astro`) + `robots.txt`
- [x] Déploiement automatisé sur **Netlify**

---

## 🚧 En cours

- [ ] Optimisation Lighthouse (SEO, accessibilité, performance)
- [ ] Ajout de tests unitaires sur les composants clés

---

## 📌 À venir

- [ ] Génération automatique du sitemap.xml
- [ ] Intégration d’analytics (Plausible, Umami ou GA4)
- [ ] Ajout d’un formulaire de contact avec envoi via service externe (Formspree, Netlify Forms, etc.)
- [ ] Expérimentations en **WebXR / AR** directement sur le portfolio
- [ ] Refonte graphique V2 (nouvelles maquettes Figma, refonte UI/UX)

---

## 🗂️ Organisation du travail

- **Branche principale** : `main` → déploiement auto via Netlify
- **Branches de features** : `feature/[nom]` → pour chaque nouvelle fonctionnalité
- **Issues GitHub** pour le suivi des tâches et corrections

---

## 📅 Objectif

- **Version stable 1.0** : Portfolio complet bilingue avec SEO et projets affichés (✅ déjà atteint)
- **Version 1.5** : Ajout animations, optimisations SEO et accessibilité (🚧 en cours)
- **Version 2.0** : Refonte graphique + nouvelles fonctionnalités (📌 à venir)
