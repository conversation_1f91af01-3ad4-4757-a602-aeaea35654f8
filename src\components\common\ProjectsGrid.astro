---
import type { Project } from "../../data/types";
import ProjectCard from "./ProjectCard.astro";
import projects from "../../data/projects.json";

export interface Props {
  lang: "fr" | "en";
  filterTag?: string;
  showFeaturedFirst?: boolean;
}
const { lang, filterTag, showFeaturedFirst = true } = Astro.props;

// Cast typé pour l'éditeur, tri + filtre
let list = (projects as Project[]).slice();

if (showFeaturedFirst) {
  list.sort(
    (a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0) || b.year - a.year
  );
} else {
  list.sort((a, b) => b.year - a.year);
}
if (filterTag) {
  list = list.filter((p) => p.tech.includes(filterTag));
}
---

<section class="projects">
  <div class="grid">
    {list.map((p) => <ProjectCard project={p} lang={lang} />)}
  </div>
</section>

<style>
  .projects {
    padding: var(--spacing-5) 0;
  }

  .grid {
    max-width: var(--container-max-width, 1100px);
    margin: 0 auto;
    padding: 0 var(--spacing-4, 1rem);
    display: grid;
    grid-template-columns: 1fr;
    gap: clamp(1rem, 2.5vw, 1.5rem);
  }

  .grid > * {
    grid-column: 1 / -1;
  }

  @media (min-width: 600px) {
    .grid > * {
      grid-column: span 2;
    }
  }
  @media (min-width: 1024px) {
    .grid > * {
      grid-column: span 3;
    }
  }
</style>
