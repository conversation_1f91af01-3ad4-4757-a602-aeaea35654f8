---
// src/components/IntroScreen.astro
import Logo from "../common/Logo.astro";
---

<script is:inline>
  function showIntroAnimation() {
    document.documentElement.classList.add("with-intro");

    window.addEventListener("DOMContentLoaded", () => {
      const intro = document.getElementById("intro-screen");
      if (!intro) return;

      setTimeout(() => {
        intro.classList.add("intro-hidden");
        setTimeout(() => {
          intro.remove();
        }, 2000);
      }, 3000);
    });
  }

  const urlParams = new URLSearchParams(window.location.search);
  const isDevMode = urlParams.has("intro");

  if (isDevMode) {
    console.log("DEV MODE : Affichage forcé de l'intro.");
    showIntroAnimation();
  } else {
    const hasBeenOnSite = sessionStorage.getItem("hasBeenOnSite");

    if (!hasBeenOnSite) {
      const referrer = document.referrer;
      const currentHost = window.location.hostname;
      let shouldShowIntro = false;

      if (!referrer) {
        shouldShowIntro = true;
      } else {
        try {
          const referrerHost = new URL(referrer).hostname;
          if (referrerHost !== currentHost) {
            shouldShowIntro = true;
          }
        } catch (e) {
          shouldShowIntro = true;
        }
      }

      if (shouldShowIntro) {
        showIntroAnimation();
      }

      sessionStorage.setItem("hasBeenOnSite", "true");
    }
  }
</script>

<div id="intro-screen">
  <div class="intro-content">
    <Logo />
  </div>
</div>

<style>
  #intro-screen {
    position: fixed;
    inset: 0;
    background: var(--color-background);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition:
      opacity 0.8s ease,
      visibility 0.8s ease;
  }

  html.with-intro #intro-screen {
    display: flex;
  }

  #intro-screen.intro-hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
  }

  .intro-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin: auto;
    animation: fadeInUp 1s ease-out forwards;
  }
</style>
