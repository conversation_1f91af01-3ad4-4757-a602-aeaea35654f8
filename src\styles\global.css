/* /styles/global.css */

@import url("font.css");

/* ======================================================= */
/* 1. TOKENS DE DESIGN ET THÈME PAR DÉFAUT (Clair)        */
/* ======================================================= */
:root {
  --sbw: 0px;

  /* --- Tokens de couleur --- */
  --gray-0: #090b11;
  --gray-50: #141925;
  --gray-100: #283044;
  --gray-200: #3d4663;
  --gray-300: #505d84;
  --gray-400: #6474a2;
  --gray-500: #8490b5;
  --gray-600: #a3acc8;
  --gray-700: #c3cadb;
  --gray-800: #e3e6ee;
  --gray-900: #f3f4f7;
  --gray-999: #ffffff;
  --accent-light: #ffca28;
  --accent-regular: #f4b400;
  --accent-dark: #d97706;

  /* --- Tokens de mise en page --- */
  --spacing-2: 0.5rem; /* 8px */
  --spacing-4: 1rem; /* 16px */
  --spacing-5: 1.25rem; /* 20px */
  --spacing-8: 2rem; /* 32px */
  --spacing-10: 2.5rem; /* 40px */
  --spacing-15: 3.75rem; /* 60px */
  --border-radius: 0.75rem; /* 12px */

  /* --- Variables sémantiques pour le THÈME CLAIR (par défaut) --- */
  --color-background: var(--gray-999);
  --color-background-85a: color-mix(
    in srgb,
    var(--color-background) 85%,
    transparent
  );
  --color-solid: var(--gray-100);
  --color-surface: var(--gray-900);
  --color-border: var(--gray-800);
  --color-text-primary: var(--gray-0);
  --color-text-secondary: var(--gray-200);
  --accent-text-over: var(--gray-999);
  --link-color: var(--accent-dark);

  /* Variables pour la gestion du contenu */
  --container-max-width: 1200px;
}

/* ======================================================= */
/* 2. SURCHARGE POUR LE THÈME SOMBRE                       */
/* ======================================================= */
:root.theme-dark {
  --color-background: var(--gray-0);
  --color-background-85a: color-mix(
    in srgb,
    var(--color-background) 85%,
    transparent
  );
  --color-solid: var(--gray-800);
  --color-surface: var(--gray-50);
  --color-border: var(--gray-100);
  --color-text-primary: var(--gray-900);
  --color-text-secondary: var(--gray-700);
  --accent-text-over: var(--gray-0);
  --link-color: var(--accent-regular);
}

/* ======================================================= */
/* 3. STYLES GLOBAUX                                     */
/* ======================================================= */
html,
body {
  min-height: 100%;
  scroll-behavior: smooth;
  overflow-x: hidden;
}
body {
  background-color: var(--color-background);
  color: var(--color-text-secondary);
  font-family: var(--font-body);
  -webkit-font-smoothing: antialiased;
  line-height: 1.5;
}
*,
*::after,
*::before {
  box-sizing: border-box;
  margin: 0;
}

section {
  scroll-margin-top: var(--header-height, 80px); /* ou la valeur exacte */
}

a {
  color: var(--link-color);
  text-decoration: none;
}
h1,
h2,
h3,
h4,
h5 {
  line-height: 1.2;
  font-family: var(--font-brand);
  font-weight: 700;
  color: var(--color-text-primary);
}
h1 {
  font-size: var(--text-4xl);
}
h2 {
  font-size: var(--text-3xl);
}
h3 {
  font-size: var(--text-2xl);
}
h4 {
  font-size: var(--text-xl);
}
h5 {
  font-size: var(--text-lg);
}

.title-wrapper {
  display: flex;
  max-width: var(--container-max-width, 1100px);
  margin: 0 auto;
  padding: 0 var(--spacing-4, 1rem);
  align-items: center;
  margin-bottom: var(--spacing-5);
}
.title-container {
  flex: 1;
  max-width: 60%;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: var(--spacing-4);
}

.title {
  color: var(--color-text-primary);
  flex-shrink: 0;
  margin: 0;
}

.line {
  flex: 1;
  height: 4px;
  border-radius: var(--border-radius);
  background: var(--accent-regular);
}

/* ======================================================= */
/* 4. STYLES RESPONSIVES                                 */
/* ======================================================= */
@media (max-width: 1024px) {
  h1 {
    font-size: var(--text-3xl);
  }
  h2 {
    font-size: var(--text-2xl);
  }
  h3 {
    font-size: var(--text-xl);
  }
  h4 {
    font-size: var(--text-lg);
  }
  h5 {
    font-size: var(--text-md);
  }
  .title-wrapper {
    flex-direction: column;
    gap: var(--spacing-2);
  }
}

@media (max-width: 600px) {
  h1 {
    font-size: var(--text-2xl);
  }
  h2 {
    font-size: var(--text-xl);
  }
  h3 {
    font-size: var(--text-lg);
  }
  h4 {
    font-size: var(--text-md);
  }
  h5 {
    font-size: var(--text-sm);
  }
  .title-wrapper {
    flex-direction: column;
    gap: var(--spacing-2);
  }
}

/* ======================================================= */
/* 5. CLASSES UTILITAIRES                                */
/* ======================================================= */
.btn {
  display: inline-block;
  padding: 0 var(--spacing-4);
  height: 3rem;
  line-height: 3rem;
  font-family: var(--font-body);
  font-size: var(--text-base);
  font-weight: 700;
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  border-radius: var(--border-radius);
  box-sizing: border-box;
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out,
    border-color 0.2s ease-in-out;
}

.btn--primary:hover {
  transform: translateY(-1px);
}
.btn--primary:active {
  transform: translateY(0);
}

.btn--primary {
  background-color: var(--accent-regular);
  color: var(--accent-text-over);
}
.btn--primary:hover {
  background-color: var(--accent-light);
}

.btn--secondary {
  background-color: transparent;
  border: 2px solid transparent;
  border-color: var(--accent-regular);
  color: var(--accent-regular);
}

.btn--secondary:hover {
  background-color: var(--accent-regular);
  color: var(--accent-text-over);
  transform: translateY(-1px);
}

.gap-2 {
  gap: var(--spacing-2);
}
.gap-4 {
  gap: var(--spacing-4);
}
.gap-8 {
  gap: var(--spacing-8);
}
.gap-10 {
  gap: var(--spacing-10);
}
.gap-15 {
  gap: var(--spacing-15);
}

.py-2 {
  padding-top: var(--spacing-2);
  padding-bottom: var(--spacing-2);
}
.py-4 {
  padding-top: var(--spacing-4);
  padding-bottom: var(--spacing-4);
}
.py-8 {
  padding-top: var(--spacing-8);
  padding-bottom: var(--spacing-8);
}
.py-10 {
  padding-top: var(--spacing-10);
  padding-bottom: var(--spacing-10);
}
.py-15 {
  padding-top: var(--spacing-15);
  padding-bottom: var(--spacing-15);
}

.no-select {
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

/* style glass factorisé */
.glass-panel {
  border-radius: var(--glass-radius);
  border: 1px solid rgba(255, 255, 255, 0.18);
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.1)
  );
  backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturate));
  -webkit-backdrop-filter: blur(var(--glass-blur))
    saturate(var(--glass-saturate));
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

/* scroll lock */
html.qa-lock-scroll {
  overflow: hidden;
}

html.qa-lock-scroll body {
  padding-inline-end: var(--sbw);
}

html.qa-lock-scroll .sb-comp {
  padding-inline-end: var(--sbw);
}

/* mode sombre */
@media (prefers-color-scheme: dark) {
  .glass-panel {
    background: linear-gradient(
      to bottom,
      color-mix(in oklab, var(--gray-800) 20%, transparent),
      color-mix(in oklab, var(--gray-50) 10%, transparent)
    );
    border-color: rgba(255, 255, 255, 0.14);
    box-shadow: 0 12px 34px rgba(0, 0, 0, 0.35),
      inset 0 1px 0 rgba(255, 255, 255, 0.18);
  }
}
