{"name": "jimmy-portfolio", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "files": "powershell -NoProfile -ExecutionPolicy Bypass -Command \"Get-ChildItem -Recurse -Force -Name | Where-Object { $_ -notmatch '(^|\\\\)(node_modules|\\.vscode|\\.astro|\\.git)(\\\\|$)' } | Out-File -FilePath listing.txt -Encoding utf8\""}, "dependencies": {"@astrojs/sitemap": "^3.5.0", "astro": "^5.13.2"}}