---
// src/layouts/Layout.astro
import Header from "@/components/layout/Header.astro";
import Footer from "@/components/layout/Footer.astro";
import Hero from "@/components/layout/Hero.astro";
import IntroScreen from "@/components/intro/IntroScreen.astro";
import QuickAccessMenu from "@/components/layout/QuickAccessMenu.astro";
import SEO from "@/components/common/SEO.astro";

import "@/styles/global.css";
import About from "@/components/layout/About.astro";
import Projects from "@/components/layout/Projects.astro";
import Contact from "@/components/layout/Contact.astro";
import ScrollToTop from "@/components/common/ScrollToTop.astro";

export interface Props {
  i18n: any;
  lang: "fr" | "en";
  pageKey: string;
}
const { i18n, lang, pageKey } = Astro.props;
const isHome = /^\/(fr|en)\/?$/.test(Astro.url.pathname);

const pageMeta = i18n[pageKey];
const title = pageMeta?.page_name || i18n.common.site_name;
const description = pageMeta?.description || i18n.common.site_description;
---

<script is:inline>
  const theme = (() => {
    if (typeof localStorage !== "undefined" && localStorage.getItem("theme")) {
      return localStorage.getItem("theme");
    }
    if (window.matchMedia("(prefers-color-scheme: dark)").matches) {
      return "dark";
    }
    return "light";
  })();

  if (theme === "light") {
    document.documentElement.classList.remove("theme-dark");
  } else {
    document.documentElement.classList.add("theme-dark");
  }
</script>

<html lang={lang}>
  <head>
    <SEO title={title} description={description} lang={lang} />
  </head>

  <body>
    {isHome && <IntroScreen />}

    <Header i18n={i18n} lang={lang} />

    <main>
      <slot>
        <Hero i18n={i18n} lang={lang} />
        <About i18n={i18n} lang={lang} />
        <Projects i18n={i18n} lang={lang} />
        <Contact i18n={i18n} lang={lang} />
        <ScrollToTop />
        <QuickAccessMenu
          githubUsername="Jamedie"
          i18n={i18n}
          lang={lang}
          initialOpen={false}
          keycapLabel={i18n.common.keycap_label}
        />
      </slot>
    </main>

    <Footer i18n={i18n} lang={lang} />
  </body>
</html>
