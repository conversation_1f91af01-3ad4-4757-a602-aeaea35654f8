---
import Icon from "../common/Icon.astro";

export interface Props {
  i18n: any;
  lang: "fr" | "en";
}

const { i18n } = Astro.props;
const title = i18n.contact.title;
---

<section id="contact">
  <div class="title-wrapper">
    <div class="title-container">
      <h2 class="title no-select">{title}</h2>
      <div class="line no-select"></div>
    </div>
  </div>
  <div class="container">
    <p class="no-select">{i18n.contact.description}</p>
    <div class="contact-button">
      <a
        class="btn btn--primary btn-linkedIn"
        href="https://www.linkedin.com/in/jimmy-gaucher-42175510a/"
      >
        <span class="sign"><Icon icon="linkedin" size="1.25rem" /></span>
        <span class="text">LinkedIn</span>
      </a>
      <a
        class="btn btn--primary btn-mail"
        href="mailto:<EMAIL>"
      >
        <span class="sign"><Icon icon="mail" size="1.25rem" /></span>
        <span class="text">Email</span>
      </a>
    </div>
  </div>
</section>

<style>
  #contact {
    padding: var(--spacing-5) var(--spacing-4);
    background: var(--color-background);
    text-align: center;
  }

  .container {
    max-width: var(--container-max-width, 1100px);
    margin: 0 auto;
    padding: 0 var(--spacing-4, 1rem);
  }

  p {
    color: var(--color-text-secondary);
    white-space: pre-line;
  }

  .contact-button {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
    margin-top: var(--spacing-5);
  }

  .btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-4);
    height: 3rem;
    border-radius: var(--border-radius);
    background: var(--accent-regular);
    color: var(--accent-text-over);
    text-decoration: none;
    font-weight: 600;
    transition: transform 0.2s ease;
  }
  @media (max-width: 1024px) {
    .container {
      padding: 0 var(--spacing-3);
    }
  }
</style>
