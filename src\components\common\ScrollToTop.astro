---
import Icon from "./Icon.astro";
---

<script is:inline>
  const handleScroll = () => {
    const scrollBtn = document.getElementById("scrollTop");
    if (!scrollBtn) return;
    if (window.scrollY > 300) {
      scrollBtn.classList.add("show");
    } else {
      scrollBtn.classList.remove("show");
    }
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  window.addEventListener("scroll", handleScroll);
  window.addEventListener("DOMContentLoaded", () => {
    const scrollBtn = document.getElementById("scrollTop");
    if (scrollBtn) {
      scrollBtn.addEventListener("click", scrollToTop);
    }
  });
</script>

<button id="scrollTop" class="scroll-top" aria-label="Retour en haut">
  <span class="icon light"
    ><Icon icon="arrow-up" size="1.5rem" color="currentColor" /></span
  >
</button>

<style>
  .scroll-top {
    position: fixed;
    bottom: var(--spacing-15);
    right: var(--spacing-4);
    background: var(--accent-regular);
    color: var(--accent-text-over);
    border: none;
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    font-size: 1.5rem;
    font-family: "Orbitron", sans-serif;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition:
      opacity 0.3s ease,
      transform 0.3s ease;
    z-index: 999;
  }

  .scroll-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .scroll-top:hover {
    background: var(--accent-light);
    transform: translateY(-1px);
  }

  .scroll-top:active {
    transform: translateY(0);
  }

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: currentColor;
  }

  @media (max-width: 600px) {
    .scroll-top {
      display: none;
    }
  }
</style>
