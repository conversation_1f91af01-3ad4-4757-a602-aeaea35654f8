---
import ProjectsGrid from "../common/ProjectsGrid.astro";

export interface Props {
  i18n: any;
  lang: "fr" | "en";
}

const { i18n, lang } = Astro.props;
const title = i18n.projects.title;
---

<section id="projects">
  <div class="title-wrapper">
    <div class="title-container">
      <h2 class="title no-select">{title}</h2>
      <div class="line no-select"></div>
    </div>
  </div>
  <div class="container">
    <ProjectsGrid lang={lang} />
  </div>
</section>

<style>
  #projects {
    max-width: var(--container-max-width, 1100px);
    padding: var(--spacing-5) var(--spacing-4);
    background: var(--color-background);
    text-align: center;
    margin: 0 auto;
  }

  .container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-4);
    justify-content: center;
  }

  @media (max-width: 1024px) {
    .container {
      flex-direction: column;
    }
  }

  @media (max-width: 600px) {
    .container {
      gap: var(--spacing-3);
    }
  }

  @media (max-width: 400px) {
    .container {
      gap: var(--spacing-2);
    }
  }
</style>
