---
import Icon from "./Icon.astro";
---

<script is:inline>
  (() => {
    const root = document.currentScript.closest(".theme-toggle");
    const btn = root?.querySelector<HTMLButtonElement>("[data-theme-toggle]");
    const html = document.documentElement;

    const setTheme = (mode: "light" | "dark") => {
      html.classList.toggle("theme-dark", mode === "dark");
      localStorage.setItem("theme", mode);
      btn?.setAttribute("aria-pressed", String(mode === "dark"));
      document.dispatchEvent(
        new CustomEvent("theme:changed", { detail: { mode } })
      );
    };

    const getInitial = (): "light" | "dark" => {
      const saved = localStorage.getItem("theme");
      if (saved === "dark" || saved === "light") return saved;
      return window.matchMedia("(prefers-color-scheme: dark)").matches
        ? "dark"
        : "light";
    };

    const toggle = () =>
      setTheme(html.classList.contains("theme-dark") ? "light" : "dark");

    // init (et sync UI locale)
    setTheme(getInitial());

    // click local
    btn?.addEventListener("click", toggle);

    // un seul listener global pour "D"
    if (!(window as any).__themeShortcutBound) {
      window.addEventListener("keydown", (e) => {
        if (e.key?.toLowerCase() === "d") {
          // appelle simplement le bascule global en lisant la classe de <html>
          html.classList.toggle("theme-dark");
          const mode = html.classList.contains("theme-dark") ? "dark" : "light";
          localStorage.setItem("theme", mode);
          document.dispatchEvent(
            new CustomEvent("theme:changed", { detail: { mode } })
          );
        }
      });
      (window as any).__themeShortcutBound = true;
    }

    // si une autre instance change le thème, mets à jour l’état du bouton courant
    document.addEventListener("theme:changed", (e: any) => {
      btn?.setAttribute("aria-pressed", String(e.detail?.mode === "dark"));
    });
  })();
</script>

<div class="theme-toggle">
  <button
    type="button"
    data-theme-toggle
    aria-pressed="false"
    aria-label="Basculer le thème"
  >
    <span class="icon light"><Icon icon="sun" /></span>
    <span class="icon dark"><Icon icon="moon" /></span>
  </button>
</div>

<style>
  .theme-toggle svg {
    pointer-events: none;
  }

  .theme-toggle {
    color: var(--gray-100);
  }
  [data-theme-toggle] {
    background-color: var(--color-solid);
  }

  button[data-theme-toggle] {
    display: flex;
    border: 0;
    border-radius: 999rem;
    padding: 0;
    cursor: pointer;
    background-color: var(--gray-999);
    box-shadow: inset 0 0 0 1px var(--accent-overlay);
  }
  .icon {
    z-index: 1;
    position: relative;
    display: flex;
    padding: 0.5rem;
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
    color: var(--accent-overlay);
  }
  .icon.light::before {
    content: "";
    z-index: -1;
    position: absolute;
    inset: 0;
    border-radius: 999rem;
    background-color: var(--accent-regular);
  }
  :global(.theme-dark) .icon.light::before {
    transform: translateX(100%);
  }
  :global(.theme-dark) .icon.light,
  :global(html:not(.theme-dark)) .icon.dark {
    color: var(--accent-text-over);
  }
  @media (prefers-reduced-motion: no-preference) {
    .icon,
    .icon.light::before {
      transition:
        transform 0.3s ease,
        color 0.3s ease;
    }
  }
</style>
